import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/date_picker_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/text_field_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/counter_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/dropdown_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/checkbox_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/multi_select_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/radio_button_widget.dart';

@RoutePage()
class QPMDPage extends StatefulWidget {
  final Question? question;

  const QPMDPage({
    super.key,
    this.question,
  });

  @override
  State<QPMDPage> createState() => _QPMDPageState();
}

class _QPMDPageState extends State<QPMDPage> {
  // State management for different measurement types
  final Map<num, dynamic> _measurementValues = {};

  @override
  void initState() {
    super.initState();
    _initializeMeasurementValues();
  }

  void _initializeMeasurementValues() {
    if (widget.question?.measurements != null) {
      for (final measurement in widget.question!.measurements!) {
        if (measurement.measurementId != null) {
          // Initialize with default values based on measurement type
          switch (measurement.measurementTypeId) {
            case 9: // Date picker
              _measurementValues[measurement.measurementId!] = null;
              break;
            case 1: // Text field
            case 2: // Text field
              _measurementValues[measurement.measurementId!] = '';
              break;
            case 7: // Counter
              _measurementValues[measurement.measurementId!] = 0;
              break;
            case 4: // Dropdown
            case 5: // Dropdown
              _measurementValues[measurement.measurementId!] = null;
              break;
            case 3: // Checkbox
              _measurementValues[measurement.measurementId!] = false;
              break;
            case 6: // Multi-select
              _measurementValues[measurement.measurementId!] = <String>[];
              break;
            default:
              _measurementValues[measurement.measurementId!] = null;
          }
        }
      }
    }
  }

  void _updateMeasurementValue(num measurementId, dynamic value) {
    setState(() {
      _measurementValues[measurementId] = value;
    });
  }

  /// Check if a measurement is required based on measurement_validations array
  bool _isMeasurementRequired(Measurement measurement) {
    if (measurement.measurementValidations == null) return false;

    for (final validation in measurement.measurementValidations!) {
      if (validation.required == true) {
        return true;
      }
    }
    return false;
  }

  /// Get camera icon info for a measurement based on photo_tags_three array
  Map<String, dynamic> _getCameraIconInfo(Measurement measurement) {
    final result = {'show': false, 'isMandatory': false};

    if (widget.question?.photoTagsThree == null ||
        measurement.measurementId == null) {
      return result;
    }

    // Check photo_tags_three array for matching questionpart_id and measurement_id
    for (final photoTag in widget.question!.photoTagsThree!) {
      if (photoTag.measurementId == measurement.measurementId) {
        result['show'] = true;
        result['isMandatory'] = photoTag.isMandatory == true;
        break;
      }
    }

    return result;
  }

  Widget _buildMeasurementWidget(Measurement measurement) {
    final measurementId = measurement.measurementId;
    if (measurementId == null) return const SizedBox.shrink();

    final isRequired = _isMeasurementRequired(measurement);
    final cameraInfo = _getCameraIconInfo(measurement);

    Widget measurementWidget;
    switch (measurement.measurementTypeId) {
      case 9: // Date picker
        measurementWidget = DatePickerWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
        );
        break;
      case 1: // Text field
      case 2: // Text field
        measurementWidget = TextFieldWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? '',
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
        );
        break;
      case 7: // Counter
        measurementWidget = CounterWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? 0,
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
        );
        break;
      case 4: // Dropdown
      case 5: // Dropdown
        measurementWidget = DropdownWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
        );
        break;
      case 3: // Checkbox
        measurementWidget = CheckboxWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? false,
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
        );
        break;
      case 6: // Multi-select
        measurementWidget = MultiSelectWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? <String>[],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
        );
        break;
      case 8: // Radio button (single choice)
        measurementWidget = RadioButtonWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
        );
        break;
      default:
        measurementWidget = Card(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              'Unsupported measurement type: ${measurement.measurementTypeId}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.blackTint1,
                  ),
            ),
          ),
        );
    }

    // Wrap the measurement widget with indicators
    return _buildMeasurementWithIndicators(
      measurementWidget,
      isRequired: isRequired,
      showCameraIcon: cameraInfo['show'] as bool,
      isCameraMandatory: cameraInfo['isMandatory'] as bool,
    );
  }

  /// Wraps a measurement widget with required and camera indicators
  Widget _buildMeasurementWithIndicators(
    Widget measurementWidget, {
    required bool isRequired,
    required bool showCameraIcon,
    required bool isCameraMandatory,
  }) {
    return Stack(
      children: [
        measurementWidget,
        // Required indicator (exclamation mark)
        if (isRequired)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              width: 20,
              height: 20,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.priority_high,
                color: Colors.white,
                size: 14,
              ),
            ),
          ),
        // Camera icon
        if (showCameraIcon)
          Positioned(
            top: isRequired
                ? 32
                : 8, // Position below required indicator if both exist
            right: 8,
            child: GestureDetector(
              onTap: () {
                // TODO: Implement camera functionality
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isCameraMandatory ? Colors.amber : Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.camera_alt,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final measurements = widget.question?.measurements ?? [];

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.question?.questionDescription ?? 'QPMD Page',
      ),
      body: measurements.isEmpty
          ? Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  'No measurements available for this question',
                  style: textTheme.bodyLarge?.copyWith(
                    color: AppColors.blackTint1,
                  ),
                ),
              ),
            )
          : SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Gap(8),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8.0,
                      vertical: 8.0,
                    ),
                    itemCount: measurements.length,
                    itemBuilder: (context, index) {
                      final measurement = measurements[index];
                      return _buildMeasurementWidget(measurement);
                    },
                  ),
                  const Gap(24),
                ],
              ),
            ),
    );
  }
}
