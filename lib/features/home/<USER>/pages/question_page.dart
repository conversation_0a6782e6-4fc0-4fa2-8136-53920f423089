import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart'; // For generated routes

@RoutePage()
class QuestionPage extends StatefulWidget {
  final entities.Form form;

  const QuestionPage({
    super.key,
    required this.form,
  });

  @override
  State<QuestionPage> createState() => _QuestionPageState();
}

class _QuestionPageState extends State<QuestionPage> {
  // State for comment fields
  final Map<num, String?> _selectedCommentTypes = {};
  final Map<num, TextEditingController> _commentControllers = {};
  // Get questions from the form
  List<entities.Question>? get questionItems => widget.form.questions;

  @override
  void initState() {
    super.initState();
    if (widget.form.questions != null) {
      for (final question in widget.form.questions!) {
        if (question.isComment == true && question.questionId != null) {
          _commentControllers[question.questionId!] = TextEditingController();
          // Initialize with null or a default first item if applicable
          _selectedCommentTypes[question.questionId!] = null;
        }
      }
    }
  }

  @override
  void dispose() {
    _commentControllers.forEach((_, controller) => controller.dispose());
    super.dispose();
  }

  /// Check if a question is mandatory based on the complex logic provided
  bool _isQuestionMandatory(entities.Question question) {
    if (question.isMulti != true && question.isComment != true) {
      // Loop through measurements, then loop through measurement_validations to check if any "required" key is true
      if (question.measurements != null) {
        for (final measurement in question.measurements!) {
          if (measurement.measurementValidations != null) {
            for (final validation in measurement.measurementValidations!) {
              if (validation.required == true) {
                return true;
              }
            }
          }
        }
      }
    } else if (question.isMulti != true) {
      // Check if isSupplementaryQuestion is true and isOneOption is true
      final isSupplementaryQuestion = question.multiMeasurementId != null &&
          question.multiMeasurementId.toString() != "0" &&
          question.multiMeasurementId.toString().isNotEmpty;

      if (isSupplementaryQuestion) {
        // Check if isOneOption is true (any questionpart_id contains "-")
        if (question.questionParts != null) {
          for (final questionPart in question.questionParts!) {
            if (questionPart.questionpartId != null &&
                questionPart.questionpartId.toString().contains("-")) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  /// Get camera icon info for a question based on photo_tags_two array
  Map<String, dynamic> _getCameraIconInfo(entities.Question question) {
    final result = {'show': false, 'isMandatory': false};

    if (question.photoTagsTwo == null || question.photoTagsTwo!.isEmpty) {
      return result;
    }

    result['show'] = true;

    // Check if any item has is_mandatory set to true
    for (final photoTag in question.photoTagsTwo!) {
      if (photoTag.isMandatory == true) {
        result['isMandatory'] = true;
        break;
      }
    }

    return result;
  }

  /// Check if question has a photo URL
  bool _hasPhotoUrl(entities.Question question) {
    return question.photoUrl != null && question.photoUrl!.isNotEmpty;
  }

  /// Build question card with indicators
  Widget _buildQuestionCardWithIndicators({
    required entities.Question question,
    required double progress,
    required String progressText,
    required bool isMandatory,
    required bool showCameraIcon,
    required bool isCameraMandatory,
    required bool hasPhotoUrl,
  }) {
    return Stack(
      children: [
        Card(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(8),
            onTap: () {
              final questionParts = question.questionParts ?? [];
              final hasSignature = question.hasSignature ?? false;
              final isMulti = question.isMulti ?? false;

              if (questionParts.length != 1 || hasSignature) {
                if (isMulti) {
                  AutoRouter.of(context).push(const FQPDRoute());
                } else {
                  AutoRouter.of(context).push(SubHeaderRoute(
                    title: question.questionDescription ?? 'Details',
                    questionParts: questionParts,
                    question: question,
                  ));
                }
              } else {
                AutoRouter.of(context).push(QPMDRoute(question: question));
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    question.questionDescription ?? 'Unnamed Question',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.black,
                        ),
                  ),
                  const Gap(8),
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: AppColors.lightGrey2,
                    valueColor: const AlwaysStoppedAnimation<Color>(
                        AppColors.primaryBlue),
                  ),
                  const Gap(4),
                  Text(
                    progressText,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.blackTint1,
                        ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // Mandatory indicator (exclamation mark)
        if (isMandatory)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              width: 20,
              height: 20,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.priority_high,
                color: Colors.white,
                size: 14,
              ),
            ),
          ),
        // Photo URL indicator (blue image icon)
        if (hasPhotoUrl)
          Positioned(
            top: isMandatory ? 32 : 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                // Open image in viewer using WebBrowserRoute
                if (question.photoUrl != null) {
                  AutoRouter.of(context)
                      .push(WebBrowserRoute(url: question.photoUrl!));
                }
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.image,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        // Camera icon
        if (showCameraIcon)
          Positioned(
            top: _calculateCameraIconTop(isMandatory, hasPhotoUrl),
            right: 8,
            child: GestureDetector(
              onTap: () {
                // TODO: Implement camera functionality
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isCameraMandatory ? Colors.amber : Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.camera_alt,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Calculate the top position for camera icon based on other indicators
  double _calculateCameraIconTop(bool isMandatory, bool hasPhotoUrl) {
    if (isMandatory && hasPhotoUrl) {
      return 56; // Below both mandatory and photo indicators
    } else if (isMandatory || hasPhotoUrl) {
      return 32; // Below one indicator
    } else {
      return 8; // No other indicators
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: widget.form.formName ?? 'Questions',
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Gap(8),
            questionItems == null || questionItems!.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text(
                        'No questions available for this form',
                        style: textTheme.bodyLarge,
                      ),
                    ),
                  )
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    itemCount: questionItems!.length,
                    itemBuilder: (context, index) {
                      final question = questionItems![index];

                      if (question.isComment == true &&
                          question.questionId != null) {
                        final questionId = question.questionId!;
                        // Ensure controller and selected value exists, though initState should handle this
                        if (!_commentControllers.containsKey(questionId)) {
                          _commentControllers[questionId] =
                              TextEditingController();
                        }
                        if (!_selectedCommentTypes.containsKey(questionId)) {
                          _selectedCommentTypes[questionId] = null;
                        }

                        final List<entities.CommentType> commentTypes =
                            question.commentTypes ?? [];

                        return Card(
                          margin: const EdgeInsets.symmetric(
                              vertical:
                                  4.0), // Consistent with ProgressItemCard if it has margin
                          elevation: 1,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  question.questionDescription ?? 'Comment',
                                  style: textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.black,
                                  ),
                                ),
                                const Gap(12),
                                if (commentTypes.isNotEmpty)
                                  DropdownButtonFormField<String>(
                                    decoration: InputDecoration(
                                      labelText: 'Comment Type',
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      filled: true,
                                      fillColor: Colors.white,
                                    ),
                                    value: _selectedCommentTypes[questionId],
                                    hint: const Text('Select one...'),
                                    isExpanded: true,
                                    items: commentTypes.map((commentType) {
                                      return DropdownMenuItem<String>(
                                        value: commentType
                                            .commentType, // Assuming commentType string is unique enough for value
                                        child: Text(commentType.commentType ??
                                            'Unnamed Type'),
                                      );
                                    }).toList(),
                                    onChanged: (String? newValue) {
                                      setState(() {
                                        _selectedCommentTypes[questionId] =
                                            newValue;
                                      });
                                    },
                                    validator: (value) {
                                      if (question.isCommentMandatory == true &&
                                          value == null) {
                                        return 'Please select a comment type.';
                                      }
                                      return null;
                                    },
                                  ),
                                if (commentTypes.isNotEmpty) const Gap(12),
                                TextFormField(
                                  controller: _commentControllers[questionId],
                                  decoration: InputDecoration(
                                    labelText: 'Your comment',
                                    hintText: 'Enter your comment here...',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    filled: true,
                                    fillColor: Colors.white,
                                  ),
                                  maxLines: 4,
                                  validator: (value) {
                                    if (question.isCommentMandatory == true &&
                                        (value == null || value.isEmpty)) {
                                      return 'Comment cannot be empty.';
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      } else {
                        // Calculate progress based on measurements if available
                        double progress = 0.0;
                        String progressText = '0 of 0';

                        if (question.measurements != null &&
                            question.measurements!.isNotEmpty) {
                          int totalMeasurements = question.measurements!.length;
                          // This could be enhanced later with actual progress tracking
                          progressText = '0 of $totalMeasurements';
                        }

                        final isMandatory = _isQuestionMandatory(question);
                        final cameraInfo = _getCameraIconInfo(question);
                        final hasPhoto = _hasPhotoUrl(question);

                        return _buildQuestionCardWithIndicators(
                          question: question,
                          progress: progress,
                          progressText: progressText,
                          isMandatory: isMandatory,
                          showCameraIcon: cameraInfo['show'] as bool,
                          isCameraMandatory: cameraInfo['isMandatory'] as bool,
                          hasPhotoUrl: hasPhoto,
                        );
                      }
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return const Gap(8);
                    },
                  ),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}
