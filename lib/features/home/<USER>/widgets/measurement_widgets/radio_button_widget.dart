import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class RadioButtonWidget extends StatelessWidget {
  final Measurement measurement;
  final String? value;
  final Function(String?) onChanged;
  final Widget? conditionalWidget; // Optional conditional widget to show

  const RadioButtonWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
    this.conditionalWidget,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      elevation: 1,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              measurement.measurementDescription ?? 'Select an option',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
            const Gap(12),
            ...options.map((option) {
              final optionId = option.measurementOptionId?.toString();
              final isSelected = value == optionId;

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: InkWell(
                  onTap: () => onChanged(optionId),
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12.0,
                      vertical: 16.0,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.blackTint2),
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.white,
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.primaryBlue
                                : Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppColors.primaryBlue
                                  : AppColors.blackTint2,
                              width: 1.5,
                            ),
                          ),
                          child: isSelected
                              ? Container(
                                  width: 12,
                                  height: 12,
                                  margin: const EdgeInsets.all(6),
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                )
                              : null,
                        ),
                        const Gap(12),
                        Expanded(
                          child: Text(
                            option.measurementOptionDescription ?? 'Option',
                            style: textTheme.bodyMedium?.copyWith(
                              color: AppColors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
            if (conditionalWidget != null) conditionalWidget!,
            if (measurement.required == true && value == null)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'This field is required',
                  style: textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
