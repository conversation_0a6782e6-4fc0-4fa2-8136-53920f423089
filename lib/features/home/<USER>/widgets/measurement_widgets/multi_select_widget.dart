import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class MultiSelectWidget extends StatelessWidget {
  final Measurement measurement;
  final List<String> value;
  final Function(List<String>) onChanged;

  const MultiSelectWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
  });

  void _showMultiSelectBottomSheet(BuildContext context) {
    final options = measurement.measurementOptions ?? [];
    final selectedIds = List<String>.from(value);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            // Calculate dynamic height based on content
            const itemHeight = 56.0; // Height per ListTile
            const headerHeight = 80.0; // Handle bar + header
            const bottomPadding = 16.0; // Bottom gap
            final maxContentHeight = MediaQuery.of(context).size.height * 0.7;
            final calculatedHeight =
                headerHeight + (options.length * itemHeight) + bottomPadding;
            final finalHeight = calculatedHeight > maxContentHeight
                ? maxContentHeight
                : calculatedHeight;

            return Container(
              height: finalHeight,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.blackTint2,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  // Header
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Select locations',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.black,
                                  ),
                        ),
                        TextButton(
                          onPressed: () {
                            onChanged(selectedIds);
                            Navigator.pop(context);
                          },
                          child: Text(
                            'Confirm',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Options list
                  Expanded(
                    child: ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      itemCount: options.length,
                      itemBuilder: (context, index) {
                        final option = options[index];
                        final optionId =
                            option.measurementOptionId?.toString() ?? '';
                        final isSelected = selectedIds.contains(optionId);

                        return InkWell(
                          onTap: () {
                            setModalState(() {
                              if (isSelected) {
                                selectedIds.remove(optionId);
                              } else {
                                selectedIds.add(optionId);
                              }
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 16.0),
                            child: Row(
                              children: [
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? AppColors.primaryBlue
                                        : Colors.white,
                                    borderRadius: BorderRadius.circular(2),
                                    border: Border.all(
                                      color: isSelected
                                          ? AppColors.primaryBlue
                                          : AppColors.blackTint2,
                                      width: 1.5,
                                    ),
                                  ),
                                  child: isSelected
                                      ? const Icon(
                                          Icons.check,
                                          size: 18,
                                          color: Colors.white,
                                        )
                                      : null,
                                ),
                                const Gap(16),
                                Expanded(
                                  child: Text(
                                    option.measurementOptionDescription ??
                                        'Unnamed Option',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium
                                        ?.copyWith(
                                          color: AppColors.black,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  const Gap(16),
                ],
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final options = measurement.measurementOptions ?? [];

    // Get selected option descriptions
    final selectedDescriptions = value
        .map((id) {
          final option = options.firstWhere(
            (opt) => opt.measurementOptionId?.toString() == id,
            orElse: () => MeasurementOption(),
          );
          return option.measurementOptionDescription ?? 'Unknown';
        })
        .where((desc) => desc != 'Unknown')
        .toList();

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      elevation: 1,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              measurement.measurementDescription ?? 'Multi-Select',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
            const Gap(12),
            InkWell(
              onTap: () => _showMultiSelectBottomSheet(context),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 16.0,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blackTint2),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        selectedDescriptions.isNotEmpty
                            ? selectedDescriptions.join(', ')
                            : 'Select options...',
                        style: textTheme.bodyMedium?.copyWith(
                          color: selectedDescriptions.isNotEmpty
                              ? AppColors.black
                              : AppColors.blackTint1,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.primaryBlue,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
            if (selectedDescriptions.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  '${selectedDescriptions.length} option(s) selected',
                  style: textTheme.bodySmall?.copyWith(
                    color: AppColors.blackTint1,
                  ),
                ),
              ),
            if (measurement.required == true && value.isEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'This field is required',
                  style: textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
