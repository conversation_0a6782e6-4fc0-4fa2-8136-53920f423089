import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class CheckboxWidget extends StatelessWidget {
  final Measurement measurement;
  final bool value;
  final Function(bool) onChanged;

  const CheckboxWidget({
    super.key,
    required this.measurement,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      elevation: 1,
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              measurement.measurementDescription ?? 'Checkbox',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.black,
              ),
            ),
            const Gap(12),
            InkWell(
              onTap: () => onChanged(!value),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12.0,
                  vertical: 16.0,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.blackTint2),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: value ? AppColors.primaryBlue : Colors.white,
                        borderRadius: BorderRadius.circular(2),
                        border: Border.all(
                          color: value
                              ? AppColors.primaryBlue
                              : AppColors.blackTint2,
                          width: 1.5,
                        ),
                      ),
                      child: value
                          ? const Icon(
                              Icons.check,
                              size: 18,
                              color: Colors.white,
                            )
                          : null,
                    ),
                    const Gap(12),
                    Expanded(
                      child: Text(
                        measurement.measurementDescription ??
                            'Check this option',
                        style: textTheme.bodyMedium?.copyWith(
                          color: AppColors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (measurement.required == true && !value)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  'This field is required',
                  style: textTheme.bodySmall?.copyWith(
                    color: Colors.red,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
