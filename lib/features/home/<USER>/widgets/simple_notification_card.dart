import 'package:flutter/material.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

import '../../../../config/themes/app_colors.dart';
import '../../../../core/constants/app_assets.dart';

enum SimpleNotificationType {
  urgent,
  manager,
  info,
}

class SimpleNotificationCard extends StatelessWidget {
  final SimpleNotificationType type;
  final String message;
  final String? title; // Optional title for info type

  const SimpleNotificationCard({
    super.key,
    required this.type,
    required this.message,
    this.title,
  });

  // Get icon asset based on notification type
  String _getIconAsset() {
    switch (type) {
      case SimpleNotificationType.urgent:
        return AppAssets.alertLightening;
      case SimpleNotificationType.manager:
        return AppAssets.alertMessage;
      case SimpleNotificationType.info:
        return AppAssets.supportDocument;
    }
  }

  // Get icon color based on notification type
  Color _getIconColor() {
    switch (type) {
      case SimpleNotificationType.urgent:
        return AppColors.richOrange;
      case SimpleNotificationType.manager:
        return AppColors.black;
      case SimpleNotificationType.info:
        return AppColors.black;
    }
  }

  // Get background color based on notification type
  Color _getBackgroundColor() {
    switch (type) {
      case SimpleNotificationType.urgent:
        return AppColors.richOrange15;
      case SimpleNotificationType.manager:
        return AppColors.lightGrey2;
      case SimpleNotificationType.info:
        return AppColors.lightGrey2;
    }
  }

  // Get text color based on notification type
  Color _getTextColor() {
    switch (type) {
      case SimpleNotificationType.urgent:
        return AppColors.darkOrange50;
      case SimpleNotificationType.manager:
        return AppColors.black;
      case SimpleNotificationType.info:
        return AppColors.black;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Icon
            SizedBox(
              width: 16,
              height: 16,
              child: Image.asset(
                _getIconAsset(),
                color: _getIconColor(),
                scale: 3,
              ),
            ),
            const SizedBox(width: 12),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title (only for info type)
                  if (type == SimpleNotificationType.info && title != null) ...[
                    Text(
                      title!,
                      style: textTheme.montserratTitleXxsmall.copyWith(
                        color: _getTextColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],

                  // Message
                  Text(
                    message,
                    style: textTheme.montserratParagraphXsmall.copyWith(
                      color: _getTextColor(),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
