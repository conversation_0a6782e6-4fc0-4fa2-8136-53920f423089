import 'package:storetrack_app/features/auth/data/models/login_response.dart';

import 'storage_service.dart';

/// Interface for token management
abstract class DataManager {
  // Save login response
  Future<void> saveLoginResponse(LoginResponse loginResponse);

  // Get login response
  Future<LoginResponse?> getLoginResponse();

  /// Save auth token
  Future<void> saveAuthToken(String token);

  /// Get auth token
  Future<String?> getAuthToken();

  /// Save FCM token
  Future<void> saveFcmToken(String token);

  /// Get FCM token
  Future<String?> getFcmToken();

  /// Clear all data
  Future<void> clearAll();

  // Save email
  Future<void> saveEmail(String email);

  // Get email
  Future<String?> getEmail();

  // Get user id
  Future<String?> getUserId();

  // Save task order
  Future<void> saveTaskOrder(List<String> taskIds);

  // Get task order
  Future<List<String>?> getTaskOrder();
}

/// A simple implementation of [DataManager]
class DataManagerImpl implements DataManager {
  /// Constructor
  DataManagerImpl(this._storage);

  final StorageService _storage;

  static const _loginResponseKey = "Constants.loginResponse";
  static const _authTokenKey = "Constants.authToken";
  static const _fcmTokenKey = "Constants.fcmToken";
  static const _taskOrderKey = "Constants.taskOrder";

  @override
  Future<void> clearAll() async {
    await _storage.clearAll();
  }

  @override
  Future<LoginResponse?> getLoginResponse() async {
    final value =
        await _storage.readData<Map<String, dynamic>>(_loginResponseKey);
    if (value != null) {
      return LoginResponse.fromJson(value);
    }
    return null;
  }

  @override
  Future<void> saveLoginResponse(LoginResponse loginResponse) {
    var loginResponseJson = loginResponse.toJson();
    return _storage.writeData(_loginResponseKey, loginResponseJson);
  }

  @override
  Future<void> saveAuthToken(String token) {
    return _storage.writeData(_authTokenKey, token);
  }

  @override
  Future<String?> getAuthToken() async {
    return await _storage.readData(_authTokenKey) as String?;
  }

  @override
  Future<void> saveFcmToken(String token) {
    return _storage.writeData(_fcmTokenKey, token);
  }

  @override
  Future<String?> getFcmToken() async {
    return await _storage.readData(_fcmTokenKey) as String?;
  }

  @override
  Future<void> saveEmail(String email) {
    return _storage.writeData("email", email);
  }

  @override
  Future<String?> getEmail() async {
    return await _storage.readData("email") as String?;
  }

  @override
  Future<String?> getUserId() async {
    return await getLoginResponse()
        .then((value) => value?.data?.userId.toString());
  }

  @override
  Future<void> saveTaskOrder(List<String> taskIds) {
    return _storage.writeData(_taskOrderKey, taskIds);
  }

  @override
  Future<List<String>?> getTaskOrder() async {
    final value = await _storage.readData<List<dynamic>>(_taskOrderKey);
    if (value != null) {
      return value.cast<String>();
    }
    return null;
  }
}
