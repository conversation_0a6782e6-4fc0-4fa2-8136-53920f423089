import 'package:realm/realm.dart';

import '../../features/home/<USER>/models/task_detail_model.dart';

class RealmDatabase {
  static RealmDatabase? _instance;
  late Realm _realm;

  RealmDatabase._() {
    final config = Configuration.local([
      TaskDetailModel.schema,
      PhotoFolderModel.schema,
      SignatureFolderModel.schema,
      FormModel.schema,
      QuestionAnswerModel.schema,
      PosItemModel.schema,
      DocumentModel.schema,
      TaskalertModel.schema,
      TaskmemberModel.schema,
      FollowupTaskModel.schema,
      StocktakeModel.schema,
      CalendarInfoModel.schema,
    ]);
    _realm = Realm(config);
  }

  static RealmDatabase get instance {
    _instance ??= RealmDatabase._();
    return _instance!;
  }

  Realm get realm => _realm;

  void close() {
    _realm.close();
  }
}
